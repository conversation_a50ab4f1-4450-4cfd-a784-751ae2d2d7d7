<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Models\UserWorking;
use App\Models\InOutGeneral;
use App\Models\InOutsOnline;
use App\Models\AbsenceLetter;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class AttendanceSummaryCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'attendance:summary {department_id?} {date?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Tổng hợp chuyên cần của User hàng ngày (check in/out, online, nghỉ phép)';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try {
            // Xử lý tham số ngày
            $dateInput = $this->argument('date');
            $departmentId = $this->argument('department_id');
            
            if (!$dateInput || $dateInput === 'now') {
                $date = Carbon::now()->format('Y-m-d');
            } else {
                // Validate date format
                try {
                    $date = Carbon::createFromFormat('Y-m-d', $dateInput)->format('Y-m-d');
                } catch (\Exception $e) {
                    $this->error('Định dạng ngày không hợp lệ. Vui lòng sử dụng định dạng YYYY-MM-DD');
                    return 1;
                }
            }

            $this->info("=== TỔNG HỢP CHUYÊN CẦN NGÀY {$date} ===");
            $this->line('');

            // Lấy danh sách tất cả nhân viên đang active (không bao gồm admin)
            $ActiveUsers = $this->getTotalActiveUsers($departmentId);

            //dd($ActiveUsers->toArray());
            $this->info("📊 Tổng số nhân viên đang hoạt động: {$ActiveUsers->count()}");
            $this->line('');

            // 1. Tổng hợp check in/out thực tế (từ camera AI)
            $this->info('🏢 CHẤM CÔNG THỰC TẾ (Camera AI):');
            $this->summarizePhysicalAttendance($date);
            $this->line('');

            // 2. Tổng hợp check in/out online
            $this->info('💻 CHẤM CÔNG ONLINE:');
            $this->summarizeOnlineAttendance($date);
            $this->line('');

            // 3. Tổng hợp nghỉ phép
            $this->info('🏖️ NGHỈ PHÉP:');
            $this->summarizeAbsenceLetters($date);
            $this->line('');

            // 4. Tổng hợp tổng quan
            $this->info('📈 TỔNG QUAN:');
            $this->summarizeOverall($date, $totalActiveUsers);

            $this->line('');
            $this->info('✅ Hoàn thành tổng hợp chuyên cần!');

        } catch (\Exception $e) {
            $this->error('Lỗi khi thực hiện tổng hợp chuyên cần: ' . $e->getMessage());
            Log::error('AttendanceSummaryCommand Error: ' . $e->getMessage());
            return 1;
        }

        return 0;
    }

    /**
     * Lấy tổng số nhân viên đang hoạt động
     */
    private function getTotalActiveUsers($departmentId = null)
    {
        return User::with(['workings'])
        ->whereHas('workings', function ($query) use ($departmentId) {
            $query->allActive()->notAdmin();
            if ($departmentId) {
                $query->where('department_id', $departmentId);
            }
        })
        ->limit(1)->get();
        //->get();
        // return UserWorking::notAdmin()
        //     ->when($departmentId, function ($q, $departmentId) {
        //         $q->where('department_id', $departmentId);
        //     })
        //     ->allActive()
        //     ->with('user:id,name,staff_code')
        //     ->groupBy('user_id')
        //     ->get();
    }

    /**
     * Tổng hợp chấm công thực tế từ camera AI
     */
    private function summarizePhysicalAttendance($date)
    {
        // Tổng số người đã check in/out
        $totalCheckedIn = InOutGeneral::whereDate('date', $date)
            ->whereNotNull('check_in')
            ->count();

        $totalCheckedOut = InOutGeneral::whereDate('date', $date)
            ->whereNotNull('check_out')
            ->count();

        // Số người chấm công đúng giờ (status = 1)
        $onTimeAttendance = InOutGeneral::whereDate('date', $date)
            ->where('status', InOutGeneral::STATUS_TRUE)
            ->count();

        // Số người chấm công không đúng giờ (status = 0)
        $lateAttendance = InOutGeneral::whereDate('date', $date)
            ->where('status', InOutGeneral::STATUS_FALSE)
            ->count();

        $this->line("   ✅ Đã check in: {$totalCheckedIn} người");
        $this->line("   ✅ Đã check out: {$totalCheckedOut} người");
        $this->line("   🟢 Chấm công đúng giờ: {$onTimeAttendance} người");
        $this->line("   🟡 Chấm công trễ/sớm: {$lateAttendance} người");
    }

    /**
     * Tổng hợp chấm công online
     */
    private function summarizeOnlineAttendance($date)
    {
        // Tổng số đăng ký làm việc online trong ngày
        $totalOnlineRequests = InOutsOnline::whereDate('checkin', $date)->count();

        // Số đăng ký đã được duyệt (status = 2 hoặc có approve_time)
        $approvedOnline = InOutsOnline::whereDate('checkin', $date)
            ->where(function($query) {
                $query->where('status', 2)
                      ->orWhereNotNull('approve_time');
            })
            ->count();

        // Số đăng ký chờ duyệt (status = 1 và chưa có approve_time)
        $pendingOnline = InOutsOnline::whereDate('checkin', $date)
            ->where('status', 1)
            ->whereNull('approve_time')
            ->count();

        $this->line("   📝 Tổng đăng ký online: {$totalOnlineRequests} người");
        $this->line("   ✅ Đã được duyệt: {$approvedOnline} người");
        $this->line("   ⏳ Chờ duyệt: {$pendingOnline} người");
    }

    /**
     * Tổng hợp nghỉ phép
     */
    private function summarizeAbsenceLetters($date)
    {
        // Tổng số đơn nghỉ phép trong ngày
        $totalAbsenceRequests = AbsenceLetter::where('from_date', '<=', $date)
            ->where('to_date', '>=', $date)
            ->count();

        // Số đơn đã được duyệt
        $approvedAbsence = AbsenceLetter::where('from_date', '<=', $date)
            ->where('to_date', '>=', $date)
            ->where('status', AbsenceLetter::STATUS_APPROVE)
            ->count();

        // Số đơn chờ duyệt
        $pendingAbsence = AbsenceLetter::where('from_date', '<=', $date)
            ->where('to_date', '>=', $date)
            ->where('status', AbsenceLetter::STATUS_WAIT_APPROVE)
            ->count();

        // Số đơn bị từ chối
        $rejectedAbsence = AbsenceLetter::where('from_date', '<=', $date)
            ->where('to_date', '>=', $date)
            ->where('status', AbsenceLetter::STATUS_REJECT)
            ->count();

        $this->line("   📋 Tổng đơn nghỉ phép: {$totalAbsenceRequests} đơn");
        $this->line("   ✅ Đã được duyệt: {$approvedAbsence} đơn");
        $this->line("   ⏳ Chờ duyệt: {$pendingAbsence} đơn");
        $this->line("   ❌ Bị từ chối: {$rejectedAbsence} đơn");
    }

    /**
     * Tổng hợp tổng quan
     */
    private function summarizeOverall($date, $totalActiveUsers)
    {
        // Số người đã có hoạt động chấm công (thực tế hoặc online)
        $physicalAttendance = InOutGeneral::whereDate('date', $date)->count();
        $onlineAttendance = InOutsOnline::whereDate('checkin', $date)
            ->where(function($query) {
                $query->where('status', 2)
                      ->orWhereNotNull('approve_time');
            })
            ->count();
        
        $approvedAbsence = AbsenceLetter::where('from_date', '<=', $date)
            ->where('to_date', '>=', $date)
            ->where('status', AbsenceLetter::STATUS_APPROVE)
            ->count();

        $totalAttended = $physicalAttendance + $onlineAttendance + $approvedAbsence;
        $notAttended = $totalActiveUsers - $totalAttended;

        $attendanceRate = $totalActiveUsers > 0 ? round(($totalAttended / $totalActiveUsers) * 100, 2) : 0;

        $this->line("   👥 Tổng có mặt (chấm công + online + nghỉ phép): {$totalAttended} người");
        $this->line("   ❓ Chưa có thông tin: {$notAttended} người");
        $this->line("   📊 Tỷ lệ có thông tin chuyên cần: {$attendanceRate}%");
    }
}
